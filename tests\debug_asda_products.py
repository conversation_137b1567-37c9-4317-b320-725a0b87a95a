#!/usr/bin/env python3
"""
Debug ASDA Product Extraction

Investigates why we're only getting 6 products when there should be more.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool

console = Console()

class AsdaProductDebugger:
    """Debug ASDA product extraction issues."""
    
    def __init__(self):
        self.test_url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
        
    def debug_page_structure(self):
        """Debug the page structure to understand product layout."""
        console.print("🔍 Debugging ASDA page structure...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to the page
                console.print(f"📍 Navigating to: {self.test_url}")
                nav_result = tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                console.print(f"Navigation result: {nav_result[:200]}...")
                
                # Wait for page to load
                time.sleep(3)
                
                # Check for product containers
                console.print("\n🔍 Looking for product containers...")
                container_result = tool._run(
                    instruction="Find all product containers, cards, or listings on this page. Look for elements that contain product information like names, prices, images.",
                    command_type="extract"
                )
                console.print(f"Container analysis: {container_result[:500]}...")
                
                # Count visible products
                console.print("\n📊 Counting visible products...")
                count_result = tool._run(
                    instruction="Count how many individual products are visible on this page. Look for product cards, tiles, or listings. Provide an exact count.",
                    command_type="extract"
                )
                console.print(f"Product count: {count_result}")
                
                # Check for pagination
                console.print("\n📄 Checking for pagination...")
                pagination_result = tool._run(
                    instruction="Look for pagination controls, 'Load More' buttons, 'Show More' buttons, or any indication that there are more products to load.",
                    command_type="extract"
                )
                console.print(f"Pagination analysis: {pagination_result}")
                
                # Extract first few product details
                console.print("\n🛒 Extracting first few products for comparison...")
                products_result = tool._run(
                    instruction="Extract the first 10 products visible on this page. For each product, get: name, price, and any visible details. Format as a numbered list.",
                    command_type="extract"
                )
                console.print(f"First products: {products_result}")
                
                # Check if products load dynamically
                console.print("\n⏳ Checking for dynamic loading...")
                scroll_result = tool._run(
                    instruction="Scroll down the page to see if more products load dynamically",
                    command_type="act"
                )
                console.print(f"Scroll result: {scroll_result}")
                
                # Count again after scrolling
                time.sleep(2)
                count_after_scroll = tool._run(
                    instruction="Count how many products are now visible after scrolling. Has the number increased?",
                    command_type="extract"
                )
                console.print(f"Product count after scroll: {count_after_scroll}")
                
        except Exception as e:
            console.print(f"❌ Debug failed: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def test_different_extraction_methods(self):
        """Test different ways to extract products."""
        console.print("\n🧪 Testing different extraction methods...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                # Method 1: Extract all at once
                console.print("\n📋 Method 1: Extract all products at once")
                all_products = tool._run(
                    instruction="Extract ALL products from this page. Don't limit to just a few - get every single product visible. Include name and price for each.",
                    command_type="extract"
                )
                console.print(f"All products method: {all_products[:1000]}...")
                
                # Method 2: Extract by sections
                console.print("\n📋 Method 2: Extract by page sections")
                top_section = tool._run(
                    instruction="Extract products from the top section of the page (first visible products)",
                    command_type="extract"
                )
                console.print(f"Top section: {top_section[:500]}...")
                
                middle_section = tool._run(
                    instruction="Extract products from the middle section of the page",
                    command_type="extract"
                )
                console.print(f"Middle section: {middle_section[:500]}...")
                
                bottom_section = tool._run(
                    instruction="Extract products from the bottom section of the page",
                    command_type="extract"
                )
                console.print(f"Bottom section: {bottom_section[:500]}...")
                
                # Method 3: Look for specific selectors
                console.print("\n📋 Method 3: Analyze HTML structure")
                html_analysis = tool._run(
                    instruction="Analyze the HTML structure. What CSS classes or elements contain the product information? Look for patterns like 'product-card', 'item', 'tile', etc.",
                    command_type="extract"
                )
                console.print(f"HTML analysis: {html_analysis}")
                
        except Exception as e:
            console.print(f"❌ Extraction test failed: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def compare_with_manual_count(self):
        """Compare our extraction with manual observation."""
        console.print("\n👁️ Manual comparison test...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate and take screenshot for manual verification
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                # Get page info
                page_info = tool._run(
                    instruction="Describe what you see on this page. How many products are visible? Are there any 'Show More' or pagination controls?",
                    command_type="extract"
                )
                console.print(f"Page description: {page_info}")
                
                # Try to find the exact issue
                issue_analysis = tool._run(
                    instruction="Why might a scraper only extract 6 products from this page when there are clearly more? Look for: lazy loading, pagination, 'Show More' buttons, or other dynamic content loading mechanisms.",
                    command_type="extract"
                )
                console.print(f"Issue analysis: {issue_analysis}")
                
        except Exception as e:
            console.print(f"❌ Manual comparison failed: {str(e)}")
    
    def run_full_debug(self):
        """Run complete debugging suite."""
        console.print(Panel(
            "[bold red]🐛 ASDA Product Extraction Debugger[/bold red]\n\n"
            "Investigating why only 6 products are extracted when more exist:\n"
            "• Page structure analysis\n"
            "• Product counting\n"
            "• Pagination detection\n"
            "• Dynamic loading checks\n"
            "• Multiple extraction methods\n"
            "• Manual comparison",
            title="Debug Suite"
        ))
        
        self.debug_page_structure()
        self.test_different_extraction_methods()
        self.compare_with_manual_count()
        
        console.print("\n[bold green]🔍 Debug analysis complete![/bold green]")
        console.print("Review the output above to identify why only 6 products are being extracted.")


def main():
    """Main debug execution."""
    debugger = AsdaProductDebugger()
    debugger.run_full_debug()


if __name__ == "__main__":
    main()
