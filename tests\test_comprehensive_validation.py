#!/usr/bin/env python3
"""
Comprehensive Testing Suite for Ecommerce Scraping System

This test suite validates all components of the ecommerce scraping system:
1. Pagination state management across all supported vendors
2. Image extraction validation with URL accessibility checks
3. Data completeness verification against StandardizedProduct schema
4. Popup handling verification for blocking elements
5. Session closure validation
6. Live testing on ASDA Fruit category

Usage:
    python tests/test_comprehensive_validation.py
"""

import os
import sys
import json
import time
import asyncio
import signal
import threading
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import requests
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.live import Live

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import ecommerce scraper components
from ecommerce_scraper.main import EcommerceScraper
from ecommerce_scraper.schemas.standardized_product import StandardizedProduct, StandardizedPrice
from ecommerce_scraper.state.state_manager import StateManager, PaginationState
from ecommerce_scraper.progress.progress_tracker import ProgressTracker
from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool
from ecommerce_scraper.tools.data_tools import ImageExtractor, ProductDataValidator
from ecommerce_scraper.utils.popup_handler import PopupHandler, handle_common_popups

console = Console()

class ComprehensiveTestSuite:
    """Comprehensive test suite for ecommerce scraping system."""
    
    def __init__(self):
        self.test_results = {}
        self.session_ids = []
        self.scraped_products = []
        self.test_start_time = None
        self.test_end_time = None
        
        # Test configuration
        self.test_vendor = "asda"
        self.test_category = "Fruit, Veg & Flowers"
        self.test_category_url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
        self.max_test_pages = 2  # Limit for testing
        
        # Initialize components
        self.state_manager = StateManager()
        self.image_extractor = ImageExtractor()
        self.data_validator = ProductDataValidator()
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all comprehensive tests."""
        self.test_start_time = datetime.now()
        
        console.print(Panel(
            "[bold magenta]🧪 Comprehensive Ecommerce Scraping Test Suite[/bold magenta]\n\n"
            "Testing all components of the ecommerce scraping system:\n"
            "• Pagination state management\n"
            "• Image extraction validation\n"
            "• Data completeness verification\n"
            "• Popup handling verification\n"
            "• Session closure validation\n"
            "• Live ASDA testing",
            title="Test Suite"
        ))
        
        # Run individual test components
        tests = [
            ("pagination_state_management", self.test_pagination_state_management),
            ("image_extraction_validation", self.test_image_extraction_validation),
            ("data_completeness_verification", self.test_data_completeness_verification),
            ("popup_handling_verification", self.test_popup_handling_verification),
            ("live_asda_testing", self.test_live_asda_scraping),
            ("session_closure_validation", self.test_session_closure_validation),
        ]
        
        for test_name, test_func in tests:
            console.print(f"\n[bold blue]🔍 Running {test_name.replace('_', ' ').title()}...[/bold blue]")
            try:
                result = test_func()
                self.test_results[test_name] = {
                    "status": "PASSED" if result else "FAILED",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                }
                status_icon = "✅" if result else "❌"
                console.print(f"{status_icon} {test_name.replace('_', ' ').title()}: {'PASSED' if result else 'FAILED'}")
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "ERROR",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                console.print(f"💥 {test_name.replace('_', ' ').title()}: ERROR - {str(e)}")
        
        self.test_end_time = datetime.now()
        
        # Generate final report
        self.generate_test_report()
        
        return self.test_results
    
    def test_pagination_state_management(self) -> bool:
        """Test pagination state management across vendors."""
        console.print("📄 Testing pagination state management...")
        
        try:
            # Test state creation
            session_id = f"test_pagination_{int(time.time())}"
            self.session_ids.append(session_id)
            
            state = self.state_manager.create_pagination_state(
                session_id=session_id,
                vendor=self.test_vendor,
                category=self.test_category,
                max_pages=self.max_test_pages
            )
            
            # Verify state properties
            assert state.session_id == session_id
            assert state.vendor == self.test_vendor
            assert state.category == self.test_category
            assert state.max_pages == self.max_test_pages
            assert state.current_page == 1
            assert state.products_scraped == 0
            
            # Test state updates
            state.current_page = 2
            state.products_scraped = 25
            state.last_product_url = "https://example.com/product/123"
            
            self.state_manager.update_pagination_state(state)
            
            # Verify state persistence
            loaded_state = self.state_manager.get_pagination_state(session_id, self.test_vendor, self.test_category)
            assert loaded_state is not None
            assert loaded_state.current_page == 2
            assert loaded_state.products_scraped == 25
            
            console.print("✅ Pagination state management working correctly")
            return True
            
        except Exception as e:
            console.print(f"❌ Pagination state test failed: {str(e)}")
            return False
    
    def test_image_extraction_validation(self) -> bool:
        """Test image extraction and URL validation."""
        console.print("🖼️ Testing image extraction validation...")
        
        try:
            # Test data with various image formats
            test_data = {
                "images": [
                    "https://images.asda.com/product/123.jpg",
                    "https://images.asda.com/product/456.webp",
                    "/static/images/product/789.png"
                ],
                "image_url": "https://images.asda.com/main/product.jpg"
            }
            
            # Extract images
            result = self.image_extractor._run(data=test_data, base_url="https://www.asda.com")
            extracted_images = json.loads(result)
            
            # Verify extraction
            assert len(extracted_images) > 0
            
            # Test URL accessibility (sample check)
            valid_urls = 0
            for image in extracted_images[:2]:  # Check first 2 images
                url = image.get("url", "")
                if url.startswith("http"):
                    try:
                        response = requests.head(url, timeout=5)
                        if response.status_code == 200:
                            valid_urls += 1
                    except:
                        pass  # URL not accessible, but that's ok for test data
            
            console.print(f"✅ Image extraction working correctly. Found {len(extracted_images)} images")
            return True
            
        except Exception as e:
            console.print(f"❌ Image extraction test failed: {str(e)}")
            return False
    
    def test_data_completeness_verification(self) -> bool:
        """Test data completeness against StandardizedProduct schema."""
        console.print("📊 Testing data completeness verification...")
        
        try:
            # Create test product data
            test_product_data = {
                "name": "Test ASDA Apple",
                "description": "Fresh red apples from local farms",
                "price": {"amount": 2.50, "currency": "GBP"},
                "image_url": "https://images.asda.com/apple.jpg",
                "category": self.test_category,
                "vendor": self.test_vendor,
                "weight": "1kg"
            }
            
            # Validate with StandardizedProduct schema
            product = StandardizedProduct(**test_product_data)
            
            # Verify all required fields are present
            assert product.name == "Test ASDA Apple"
            assert product.description == "Fresh red apples from local farms"
            assert product.price.amount == 2.50
            assert product.price.currency == "GBP"
            assert product.image_url == "https://images.asda.com/apple.jpg"
            assert product.category == self.test_category
            assert product.vendor == self.test_vendor
            assert product.weight == "1kg"
            assert product.scraped_at is not None
            
            # Test data validation tool
            validation_result = self.data_validator._run(data=json.dumps(test_product_data))
            assert "valid" in validation_result.lower() or "success" in validation_result.lower()
            
            console.print("✅ Data completeness verification working correctly")
            return True
            
        except Exception as e:
            console.print(f"❌ Data completeness test failed: {str(e)}")
            return False
    
    def test_popup_handling_verification(self) -> bool:
        """Test popup handling system."""
        console.print("🚫 Testing popup handling verification...")
        
        try:
            # Test popup command generation
            popup_types = ["cookie", "newsletter", "age", "location", "promotion", "app"]
            
            for popup_type in popup_types:
                command = PopupHandler.create_popup_dismissal_command(popup_type)
                assert len(command) > 0
                assert popup_type.lower() in command.lower() or "popup" in command.lower()
            
            # Test vendor-specific instructions
            asda_instructions = PopupHandler.get_vendor_specific_instructions("asda")
            assert "asda" in asda_instructions.lower()
            assert "privacy" in asda_instructions.lower() or "cookie" in asda_instructions.lower()
            
            # Test verification command
            verification_cmd = PopupHandler.get_verification_command()
            assert "content" in verification_cmd.lower()
            assert "popup" in verification_cmd.lower()
            
            console.print("✅ Popup handling system working correctly")
            return True

        except Exception as e:
            console.print(f"❌ Popup handling test failed: {str(e)}")
            return False

    def test_live_asda_scraping(self) -> bool:
        """Test live scraping on ASDA Fruit category."""
        console.print("🛒 Testing live ASDA scraping...")

        try:
            session_id = f"test_live_{int(time.time())}"
            self.session_ids.append(session_id)

            with EcommerceScraper(verbose=True) as scraper:
                console.print(f"🚀 Starting live scraping test on ASDA Fruit category...")
                console.print(f"📍 URL: {self.test_category_url}")

                # Perform live scraping
                result = scraper.scrape_category_directly(
                    category_url=self.test_category_url,
                    vendor=self.test_vendor,
                    category_name=self.test_category,
                    max_pages=self.max_test_pages
                )

                # Verify results
                assert result is not None
                assert result.success == True
                assert len(result.products) > 0

                # Store products for further validation
                self.scraped_products.extend(result.products)

                # Verify each product meets schema requirements
                for product in result.products:
                    assert hasattr(product, 'name') and product.name
                    assert hasattr(product, 'description') and product.description
                    assert hasattr(product, 'price') and product.price
                    assert hasattr(product, 'image_url') and product.image_url
                    assert hasattr(product, 'category') and product.category
                    assert hasattr(product, 'vendor') and product.vendor
                    assert hasattr(product, 'scraped_at') and product.scraped_at

                # Verify popup handling worked (no errors in agent results)
                if result.agent_results:
                    for agent_result in result.agent_results:
                        # Check that agents successfully accessed the URL
                        assert agent_result.get('url') == self.test_category_url
                        assert agent_result.get('products_found', 0) >= 0

                console.print(f"✅ Live ASDA scraping successful. Found {len(result.products)} products")

                # Save test results
                self._save_test_results(result, session_id)

                return True

        except Exception as e:
            console.print(f"❌ Live ASDA scraping test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def test_session_closure_validation(self) -> bool:
        """Test proper session closure and resource cleanup."""
        console.print("🔒 Testing session closure validation...")

        try:
            # Test Stagehand tool session management
            with EcommerceStagehandTool.create_with_context() as tool:
                # Perform a simple operation
                result = tool._run(
                    instruction="Navigate to test page",
                    url="https://demo.vercel.store/",
                    command_type="act"
                )

                # Tool should be functional
                assert "Error" not in result or "completed" in result.lower()

            # After context manager, session should be closed
            # This is verified by the context manager implementation

            # Test state manager cleanup
            for session_id in self.session_ids:
                # Verify session states are properly saved
                session_states = self.state_manager.get_session_summary(session_id)
                # Session should exist even after operations
                assert session_states is not None or "error" in str(session_states).lower()

            console.print("✅ Session closure validation successful")
            return True

        except Exception as e:
            console.print(f"❌ Session closure test failed: {str(e)}")
            return False

    def _save_test_results(self, result, session_id: str):
        """Save test results to file."""
        try:
            # Create test results directory
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)

            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_test_{timestamp}.json"
            filepath = results_dir / filename

            # Prepare results data
            test_data = {
                "test_info": {
                    "session_id": session_id,
                    "vendor": self.test_vendor,
                    "category": self.test_category,
                    "test_url": self.test_category_url,
                    "max_pages": self.max_test_pages,
                    "test_timestamp": timestamp,
                    "success": result.success,
                    "total_products": len(result.products)
                },
                "products": [product.to_dict() for product in result.products],
                "agent_results": result.agent_results if hasattr(result, 'agent_results') else []
            }

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, indent=2, ensure_ascii=False)

            console.print(f"📁 Test results saved to: {filepath}")

        except Exception as e:
            console.print(f"⚠️ Failed to save test results: {str(e)}")

    def generate_test_report(self):
        """Generate comprehensive test report."""
        console.print("\n" + "="*80)
        console.print("[bold magenta]📋 COMPREHENSIVE TEST REPORT[/bold magenta]")
        console.print("="*80)

        # Test summary table
        table = Table(title="Test Results Summary")
        table.add_column("Test Name", style="cyan")
        table.add_column("Status", style="bold")
        table.add_column("Details", style="dim")

        passed_tests = 0
        total_tests = len(self.test_results)

        for test_name, result in self.test_results.items():
            status = result["status"]
            if status == "PASSED":
                status_display = "[green]✅ PASSED[/green]"
                passed_tests += 1
            elif status == "FAILED":
                status_display = "[red]❌ FAILED[/red]"
            else:
                status_display = "[yellow]💥 ERROR[/yellow]"

            details = result.get("error", "Success") if status != "PASSED" else "All checks passed"
            table.add_row(
                test_name.replace("_", " ").title(),
                status_display,
                details[:50] + "..." if len(details) > 50 else details
            )

        console.print(table)

        # Overall summary
        console.print(f"\n[bold]Overall Results:[/bold]")
        console.print(f"✅ Passed: {passed_tests}/{total_tests}")
        console.print(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
        console.print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if self.scraped_products:
            console.print(f"🛒 Products Scraped: {len(self.scraped_products)}")

        # Test duration
        if self.test_start_time and self.test_end_time:
            duration = self.test_end_time - self.test_start_time
            console.print(f"⏱️ Test Duration: {duration.total_seconds():.1f} seconds")

        # Recommendations
        console.print(f"\n[bold]Recommendations:[/bold]")
        if passed_tests == total_tests:
            console.print("🎉 All tests passed! The ecommerce scraping system is working correctly.")
        else:
            console.print("⚠️ Some tests failed. Review the failed tests and fix issues before production use.")
            console.print("💡 Check logs and error messages for detailed troubleshooting information.")


def main():
    """Main test execution function."""

    # Handle graceful termination
    def signal_handler(signum, frame):
        console.print("\n🛑 Test interrupted by user (Ctrl+C)")
        sys.exit(130)

    signal.signal(signal.SIGINT, signal_handler)

    # Run comprehensive test suite
    test_suite = ComprehensiveTestSuite()

    try:
        results = test_suite.run_all_tests()

        # Determine exit code based on results
        failed_tests = sum(1 for result in results.values() if result["status"] != "PASSED")

        if failed_tests == 0:
            console.print("\n🎉 All tests completed successfully!")
            sys.exit(0)
        else:
            console.print(f"\n💥 {failed_tests} test(s) failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        console.print("\n🛑 Test suite interrupted by user")
        sys.exit(130)
    except Exception as e:
        console.print(f"\n💥 Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
