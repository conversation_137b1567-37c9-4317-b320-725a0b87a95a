#!/usr/bin/env python3
"""
Simple ASDA Testing Suite

Focused test for ASDA Fruit category scraping with core validation.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict
from rich.console import Console
from rich.panel import Panel

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.main import EcommerceScraper
from ecommerce_scraper.schemas.standardized_product import StandardizedProduct
from ecommerce_scraper.state.state_manager import StateManager
from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool
from ecommerce_scraper.utils.popup_handler import PopupHandler

console = Console()

class SimpleAsdaTestSuite:
    """Simple test suite focused on ASDA scraping validation."""
    
    def __init__(self):
        self.test_vendor = "asda"
        self.test_category = "Fruit, Veg & Flowers"
        self.test_category_url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
        self.max_test_pages = 1  # Just 1 page for testing
        
    def test_popup_handling(self) -> bool:
        """Test popup handling system."""
        console.print("🚫 Testing popup handling...")
        
        try:
            # Test popup command generation
            cookie_cmd = PopupHandler.create_popup_dismissal_command("cookie")
            assert "cookie" in cookie_cmd.lower() or "accept" in cookie_cmd.lower()
            
            # Test ASDA-specific instructions
            asda_instructions = PopupHandler.get_vendor_specific_instructions("asda")
            assert "asda" in asda_instructions.lower()
            
            console.print("✅ Popup handling system working")
            return True
            
        except Exception as e:
            console.print(f"❌ Popup handling test failed: {str(e)}")
            return False
    
    def test_state_management(self) -> bool:
        """Test pagination state management."""
        console.print("📄 Testing state management...")
        
        try:
            state_manager = StateManager()
            session_id = f"test_simple_{int(time.time())}"
            
            # Create state
            state = state_manager.create_pagination_state(
                session_id=session_id,
                vendor=self.test_vendor,
                category=self.test_category,
                max_pages=self.max_test_pages
            )
            
            # Verify state
            assert state.vendor == self.test_vendor
            assert state.category == self.test_category
            assert state.current_page == 1
            
            console.print("✅ State management working")
            return True
            
        except Exception as e:
            console.print(f"❌ State management test failed: {str(e)}")
            return False
    
    def test_schema_validation(self) -> bool:
        """Test StandardizedProduct schema validation."""
        console.print("📊 Testing schema validation...")
        
        try:
            # Create test product
            test_product = StandardizedProduct(
                name="Test Apple",
                description="Fresh red apple",
                price={"amount": 1.50, "currency": "GBP"},
                image_url="https://example.com/apple.jpg",
                category=self.test_category,
                vendor=self.test_vendor,
                weight="200g"
            )
            
            # Verify fields
            assert test_product.name == "Test Apple"
            assert test_product.price.amount == 1.50
            assert test_product.price.currency == "GBP"
            assert test_product.vendor == self.test_vendor
            
            console.print("✅ Schema validation working")
            return True
            
        except Exception as e:
            console.print(f"❌ Schema validation test failed: {str(e)}")
            return False
    
    def test_stagehand_session(self) -> bool:
        """Test Stagehand session management."""
        console.print("🔧 Testing Stagehand session...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Test basic navigation
                result = tool._run(
                    instruction="Navigate to demo page",
                    url="https://demo.vercel.store/",
                    command_type="act"
                )
                
                # Should not have critical errors
                assert "Error" not in result or "completed" in result.lower()
            
            console.print("✅ Stagehand session working")
            return True
            
        except Exception as e:
            console.print(f"❌ Stagehand session test failed: {str(e)}")
            return False
    
    def test_asda_scraping(self) -> bool:
        """Test actual ASDA scraping with reduced expectations."""
        console.print("🛒 Testing ASDA scraping...")
        
        try:
            session_id = f"test_asda_{int(time.time())}"
            
            with EcommerceScraper(verbose=True) as scraper:
                console.print(f"🚀 Starting ASDA scraping test...")
                console.print(f"📍 URL: {self.test_category_url}")
                
                # Perform scraping
                result = scraper.scrape_category_directly(
                    category_url=self.test_category_url,
                    vendor=self.test_vendor,
                    category_name=self.test_category,
                    max_pages=self.max_test_pages
                )
                
                # Basic validation - don't require products, just successful execution
                assert result is not None
                console.print(f"📊 Scraping completed. Success: {result.success}")
                console.print(f"📦 Products found: {len(result.products) if result.products else 0}")
                
                # Save results if any products found
                if result.products and len(result.products) > 0:
                    self._save_test_results(result, session_id)
                    
                    # Validate product schema if products exist
                    for product in result.products:
                        assert hasattr(product, 'name')
                        assert hasattr(product, 'vendor')
                        assert product.vendor == self.test_vendor
                
                console.print("✅ ASDA scraping test completed")
                return True
                
        except Exception as e:
            console.print(f"❌ ASDA scraping test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def _save_test_results(self, result, session_id: str):
        """Save test results to file."""
        try:
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asda_simple_test_{timestamp}.json"
            filepath = results_dir / filename
            
            test_data = {
                "test_info": {
                    "session_id": session_id,
                    "vendor": self.test_vendor,
                    "category": self.test_category,
                    "test_url": self.test_category_url,
                    "timestamp": timestamp,
                    "success": result.success,
                    "total_products": len(result.products) if result.products else 0
                },
                "products": [product.to_dict() for product in result.products] if result.products else []
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, indent=2, ensure_ascii=False)
            
            console.print(f"📁 Test results saved to: {filepath}")
            
        except Exception as e:
            console.print(f"⚠️ Failed to save test results: {str(e)}")
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all simple tests."""
        console.print(Panel(
            "[bold magenta]🧪 Simple ASDA Test Suite[/bold magenta]\n\n"
            "Testing core functionality:\n"
            "• Popup handling system\n"
            "• State management\n"
            "• Schema validation\n"
            "• Stagehand session management\n"
            "• Live ASDA scraping",
            title="Simple Test Suite"
        ))
        
        tests = [
            ("popup_handling", self.test_popup_handling),
            ("state_management", self.test_state_management),
            ("schema_validation", self.test_schema_validation),
            ("stagehand_session", self.test_stagehand_session),
            ("asda_scraping", self.test_asda_scraping),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            console.print(f"\n🔍 Running {test_name.replace('_', ' ').title()}...")
            try:
                result = test_func()
                results[test_name] = result
                status_icon = "✅" if result else "❌"
                console.print(f"{status_icon} {test_name.replace('_', ' ').title()}: {'PASSED' if result else 'FAILED'}")
            except Exception as e:
                results[test_name] = False
                console.print(f"💥 {test_name.replace('_', ' ').title()}: ERROR - {str(e)}")
        
        # Summary
        passed = sum(results.values())
        total = len(results)
        console.print(f"\n[bold]Simple Test Results:[/bold]")
        console.print(f"✅ Passed: {passed}/{total}")
        console.print(f"❌ Failed: {total - passed}/{total}")
        console.print(f"📊 Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            console.print("\n🎉 All simple tests passed! Core functionality is working.")
        else:
            console.print(f"\n⚠️ {total - passed} test(s) failed. Review issues before production use.")
        
        return results


def main():
    """Main test execution."""
    test_suite = SimpleAsdaTestSuite()
    results = test_suite.run_all_tests()
    
    # Exit with appropriate code
    failed_tests = sum(1 for result in results.values() if not result)
    sys.exit(0 if failed_tests == 0 else 1)


if __name__ == "__main__":
    main()
