#!/usr/bin/env python3
"""
Batch Processing Test Suite

Tests the batch processing system including:
- Job queue management
- Worker thread coordination
- Progress tracking integration
- State persistence during batch operations
- Error handling and retry logic
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from rich.console import Console

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.batch.batch_processor import BatchProcessor
from ecommerce_scraper.state.state_manager import StateManager
from ecommerce_scraper.progress.progress_tracker import ProgressTracker

console = Console()

class BatchProcessingTestSuite:
    """Test suite for batch processing functionality."""
    
    def __init__(self):
        self.state_manager = StateManager()
        self.test_session_id = f"batch_test_{int(time.time())}"
        
    def test_batch_processor_setup(self) -> bool:
        """Test batch processor initialization and configuration."""
        console.print("🔧 Testing batch processor setup...")
        
        try:
            # Create batch processor with test configuration
            batch_processor = BatchProcessor(
                max_workers=2,  # Small number for testing
                max_concurrent_vendors=1,
                output_dir="./test_batch_output",
                state_manager=self.state_manager
            )
            
            # Verify configuration
            assert batch_processor.max_workers == 2
            assert batch_processor.max_concurrent_vendors == 1
            assert batch_processor.state_manager is not None
            
            console.print("✅ Batch processor setup successful")
            return True
            
        except Exception as e:
            console.print(f"❌ Batch processor setup failed: {str(e)}")
            return False
    
    def test_job_queue_management(self) -> bool:
        """Test job creation and queue management."""
        console.print("📋 Testing job queue management...")
        
        try:
            batch_processor = BatchProcessor(
                max_workers=1,
                max_concurrent_vendors=1,
                output_dir="./test_batch_output",
                state_manager=self.state_manager
            )
            
            # Create test jobs
            test_jobs = [
                {
                    "vendor": "asda",
                    "category": "Test Category 1",
                    "url": "https://groceries.asda.com/test1",
                    "max_pages": 1,
                    "priority": 1
                },
                {
                    "vendor": "asda", 
                    "category": "Test Category 2",
                    "url": "https://groceries.asda.com/test2",
                    "max_pages": 1,
                    "priority": 2
                }
            ]
            
            # Add jobs to processor
            job_ids = batch_processor.add_url_based_jobs(test_jobs, self.test_session_id)
            
            # Verify jobs were added
            assert len(job_ids) == 2
            assert all(isinstance(job_id, str) for job_id in job_ids)
            
            # Check job statuses
            for job_id in job_ids:
                status = batch_processor.get_job_status(job_id)
                assert status is not None
                assert "status" in status
                assert status["status"] in ["pending", "running", "completed", "failed"]
            
            console.print(f"✅ Job queue management successful. Created {len(job_ids)} jobs")
            return True
            
        except Exception as e:
            console.print(f"❌ Job queue management test failed: {str(e)}")
            return False
    
    def test_progress_tracking_integration(self) -> bool:
        """Test integration with progress tracking system."""
        console.print("📊 Testing progress tracking integration...")
        
        try:
            # Create progress tracker
            progress_tracker = ProgressTracker(self.state_manager)
            
            # Start tracking
            progress_tracker.start_tracking(self.test_session_id)
            
            # Add vendor/category to track
            progress_tracker.add_vendor_category("asda", "Test Category", estimated_pages=5)
            
            # Simulate progress updates
            progress_tracker.update_vendor_progress("asda", "Test Category", current_page=2, products_found=15)
            progress_tracker.update_vendor_progress("asda", "Test Category", current_page=3, products_found=25)
            
            # Get progress snapshot
            snapshot = progress_tracker.get_progress_snapshot(self.test_session_id)
            
            # Verify snapshot
            assert snapshot is not None
            assert snapshot.session_id == self.test_session_id
            assert snapshot.total_products >= 0
            
            # Stop tracking
            progress_tracker.stop_tracking()
            
            console.print("✅ Progress tracking integration successful")
            return True
            
        except Exception as e:
            console.print(f"❌ Progress tracking integration test failed: {str(e)}")
            return False
    
    def test_state_persistence(self) -> bool:
        """Test state persistence during batch operations."""
        console.print("💾 Testing state persistence...")
        
        try:
            # Create pagination state
            state = self.state_manager.create_pagination_state(
                session_id=self.test_session_id,
                vendor="asda",
                category="Test Category",
                max_pages=5
            )
            
            # Update state multiple times
            state.current_page = 2
            state.products_scraped = 20
            state.last_product_url = "https://example.com/product/123"
            self.state_manager.update_pagination_state(state)
            
            state.current_page = 3
            state.products_scraped = 35
            state.last_product_url = "https://example.com/product/456"
            self.state_manager.update_pagination_state(state)
            
            # Verify state persistence
            loaded_state = self.state_manager.get_pagination_state(
                self.test_session_id, "asda", "Test Category"
            )
            
            assert loaded_state is not None
            assert loaded_state.current_page == 3
            assert loaded_state.products_scraped == 35
            assert loaded_state.last_product_url == "https://example.com/product/456"
            
            # Test session summary
            summary = self.state_manager.get_session_summary(self.test_session_id)
            assert summary is not None
            
            console.print("✅ State persistence test successful")
            return True
            
        except Exception as e:
            console.print(f"❌ State persistence test failed: {str(e)}")
            return False
    
    def test_error_handling(self) -> bool:
        """Test error handling and retry logic."""
        console.print("⚠️ Testing error handling...")
        
        try:
            batch_processor = BatchProcessor(
                max_workers=1,
                max_concurrent_vendors=1,
                output_dir="./test_batch_output",
                state_manager=self.state_manager
            )
            
            # Test with invalid job data
            invalid_jobs = [
                {
                    "vendor": "",  # Invalid empty vendor
                    "category": "Test Category",
                    "url": "invalid-url",  # Invalid URL
                    "max_pages": -1,  # Invalid page count
                    "priority": 1
                }
            ]
            
            # This should handle errors gracefully
            try:
                job_ids = batch_processor.add_url_based_jobs(invalid_jobs, self.test_session_id)
                # If it succeeds, check that error handling worked
                if job_ids:
                    for job_id in job_ids:
                        status = batch_processor.get_job_status(job_id)
                        # Job should exist but may be in failed state
                        assert status is not None
            except Exception:
                # Expected behavior - invalid jobs should be rejected
                pass
            
            console.print("✅ Error handling test successful")
            return True
            
        except Exception as e:
            console.print(f"❌ Error handling test failed: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all batch processing tests."""
        console.print("\n[bold blue]🧪 Batch Processing Test Suite[/bold blue]")
        
        tests = [
            ("batch_processor_setup", self.test_batch_processor_setup),
            ("job_queue_management", self.test_job_queue_management),
            ("progress_tracking_integration", self.test_progress_tracking_integration),
            ("state_persistence", self.test_state_persistence),
            ("error_handling", self.test_error_handling),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            console.print(f"\n🔍 Running {test_name.replace('_', ' ').title()}...")
            try:
                result = test_func()
                results[test_name] = result
                status_icon = "✅" if result else "❌"
                console.print(f"{status_icon} {test_name.replace('_', ' ').title()}: {'PASSED' if result else 'FAILED'}")
            except Exception as e:
                results[test_name] = False
                console.print(f"💥 {test_name.replace('_', ' ').title()}: ERROR - {str(e)}")
        
        # Summary
        passed = sum(results.values())
        total = len(results)
        console.print(f"\n[bold]Batch Processing Test Results:[/bold]")
        console.print(f"✅ Passed: {passed}/{total}")
        console.print(f"❌ Failed: {total - passed}/{total}")
        console.print(f"📊 Success Rate: {(passed/total)*100:.1f}%")
        
        return results


def main():
    """Main test execution."""
    test_suite = BatchProcessingTestSuite()
    results = test_suite.run_all_tests()
    
    # Exit with appropriate code
    failed_tests = sum(1 for result in results.values() if not result)
    sys.exit(0 if failed_tests == 0 else 1)


if __name__ == "__main__":
    main()
