# test_stagehand_fix.py
from crewai_tools import StagehandTool
from stagehand.schemas import AvailableModel
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_official_stagehand_fix():
    """Test if CrewAI StagehandTool bug is fixed."""
    print("Testing CrewAI StagehandTool for model_api_key bug fix...")
    print(f"Environment check:")
    print(f"  BROWSERBASE_API_KEY: {'✅ Set' if os.getenv('BROWSERBASE_API_KEY') else '❌ Missing'}")
    print(f"  BROWSERBASE_PROJECT_ID: {'✅ Set' if os.getenv('BROWSERBASE_PROJECT_ID') else '❌ Missing'}")
    print(f"  OPENAI_API_KEY: {'✅ Set' if os.getenv('OPENAI_API_KEY') else '❌ Missing'}")
    print()
    
    try:
        print("Creating StagehandTool instance...")
        with StagehandTool(
            api_key="bb_live_MRy36QffXPBqC3WccZpj5Lvul1E",
            project_id="3111dead-bdcf-4375-b21d-e5dbe88339dc",
            model_api_key="********************************************************************************************************************************************************************",
            model_name=AvailableModel.GPT_4O
        ) as tool:
            print("✅ StagehandTool created successfully")
            
            print("Testing navigation...")
            nav_result = tool.run(
                instruction="Navigate to the demo store",
                url="https://demo.vercel.store",
                command_type="act"
            )
            print(f"Navigation result: {nav_result}")
            
            print("Testing extraction (this is where the bug typically occurs)...")
            # Test extraction (this should work if bug is fixed)
            result = tool.run(
                instruction="Extract the page title and main heading",
                command_type="extract"
            )
            
            print(f"Extraction result: {result}")
            
            if result and "Error" not in str(result) and "OPENAI_API_KEY" not in str(result):
                print("✅ BUG APPEARS TO BE FIXED!")
                print("The model_api_key is being properly forwarded to the remote session.")
                return True
            else:
                print("❌ Bug still exists")
                print("The extraction failed, likely due to model_api_key not being forwarded.")
                return False
                
    except Exception as e:
        print(f"❌ Bug still exists: {e}")
        if "OPENAI_API_KEY" in str(e):
            print("This is the classic symptom of the model_api_key forwarding bug.")
        return False

if __name__ == "__main__":
    success = test_official_stagehand_fix()
    if success:
        print("\n🎉 You can now use the official StagehandTool instead of your custom implementation!")
    else:
        print("\n⚠️  Continue using your custom EcommerceStagehandTool until this is fixed.")
