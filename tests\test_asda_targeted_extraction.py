#!/usr/bin/env python3
"""
ASDA Targeted Fruit Extraction Test

Focuses specifically on extracting fruit products from the correct section of the page.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool

console = Console()

class AsdaTargetedExtractionTest:
    """Test targeted fruit extraction with proper section identification."""
    
    def __init__(self):
        self.test_url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
        
    def test_page_analysis(self):
        """Analyze the page structure to identify fruit product sections."""
        console.print("🔍 Analyzing page structure for fruit products...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                # Analyze page sections
                page_analysis = tool._run(
                    instruction="Analyze this page structure. Identify different sections: promotional banners, recommended products, main product grid/listing, navigation elements. Focus on where the actual fruit products are displayed.",
                    command_type="extract"
                )
                console.print(f"📊 Page analysis: {page_analysis[:800]}...")
                
                # Look for fruit-specific content
                fruit_analysis = tool._run(
                    instruction="Look specifically for fruit products on this page. Ignore promotional banners, recommended items, or other product categories. Find the main fruit product listing area.",
                    command_type="extract"
                )
                console.print(f"🍎 Fruit analysis: {fruit_analysis[:800]}...")
                
                # Check for product grid
                grid_analysis = tool._run(
                    instruction="Find the main product grid or listing area that contains the fruit products. Look for a section with multiple fruit items displayed in a grid or list format.",
                    command_type="extract"
                )
                console.print(f"📋 Grid analysis: {grid_analysis[:800]}...")
                
                return page_analysis, fruit_analysis, grid_analysis
                
        except Exception as e:
            console.print(f"❌ Page analysis failed: {str(e)}")
            return "", "", ""
    
    def test_targeted_fruit_extraction(self):
        """Extract only fruit products from the correct section."""
        console.print("\n🍎 Testing targeted fruit extraction...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                # First, identify the fruit section
                section_identification = tool._run(
                    instruction="Identify the main content area that contains fruit products. Ignore any promotional banners, recommended products, or navigation elements. Focus only on the fruit product listings.",
                    command_type="extract"
                )
                console.print(f"📍 Section identification: {section_identification[:500]}...")
                
                # Extract fruit products specifically
                fruit_products = tool._run(
                    instruction="Extract ONLY fruit products from the main product listing area. Look for items like apples, oranges, bananas, berries, melons, etc. Ignore any non-fruit items, promotional banners, or recommended products. Get name and price for each fruit product.",
                    command_type="extract"
                )
                console.print(f"🍎 Fruit products: {fruit_products[:1000]}...")
                
                # Count fruit products
                fruit_count = self._count_fruit_products(fruit_products)
                console.print(f"📊 Fruit products found: {fruit_count}")
                
                # Try scrolling to get more fruits
                console.print("\n🔄 Scrolling to load more fruit products...")
                tool._run(
                    instruction="Scroll down slowly to load more fruit products",
                    command_type="act"
                )
                time.sleep(4)
                
                # Extract again after scrolling
                more_fruits = tool._run(
                    instruction="Extract all fruit products now visible after scrolling. Focus only on fruits like apples, oranges, bananas, berries, melons, citrus fruits, etc. Ignore non-fruit items.",
                    command_type="extract"
                )
                console.print(f"🍊 More fruits after scroll: {more_fruits[:1000]}...")
                
                more_fruit_count = self._count_fruit_products(more_fruits)
                console.print(f"📊 Total fruit products after scroll: {more_fruit_count}")
                
                # Save results
                self._save_targeted_results(fruit_products, more_fruits, fruit_count, more_fruit_count)
                
                return fruit_count, more_fruit_count, fruit_products, more_fruits
                
        except Exception as e:
            console.print(f"❌ Targeted extraction failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0, 0, "", ""
    
    def test_specific_fruit_search(self):
        """Search for specific fruit types to validate extraction."""
        console.print("\n🔍 Testing specific fruit type search...")
        
        fruit_types = ["apple", "orange", "banana", "berry", "melon", "grape", "citrus"]
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                found_fruits = {}
                
                for fruit_type in fruit_types:
                    console.print(f"🔍 Looking for {fruit_type} products...")
                    
                    fruit_search = tool._run(
                        instruction=f"Look for {fruit_type} products on this page. Find any products that contain {fruit_type} in the name or are {fruit_type}-related. List the specific products you find.",
                        command_type="extract"
                    )
                    
                    found_fruits[fruit_type] = fruit_search
                    console.print(f"📦 {fruit_type} results: {fruit_search[:200]}...")
                
                # Save specific search results
                self._save_specific_search_results(found_fruits)
                
                return found_fruits
                
        except Exception as e:
            console.print(f"❌ Specific fruit search failed: {str(e)}")
            return {}
    
    def _count_fruit_products(self, result: str) -> int:
        """Count fruit products in extraction result."""
        if not result:
            return 0
        
        # Look for fruit-related keywords
        fruit_keywords = [
            "apple", "orange", "banana", "berry", "grape", "melon", "lemon", "lime",
            "strawberry", "raspberry", "blackberry", "blueberry", "cherry", "peach",
            "pear", "plum", "kiwi", "mango", "pineapple", "avocado", "grapefruit"
        ]
        
        result_lower = result.lower()
        fruit_mentions = 0
        
        for keyword in fruit_keywords:
            fruit_mentions += result_lower.count(keyword)
        
        # Also count numbered items that might be fruits
        import re
        numbered_items = re.findall(r'^\d+\.', result, re.MULTILINE)
        
        # Return the higher of the two counts
        return max(fruit_mentions, len(numbered_items))
    
    def _save_targeted_results(self, initial_fruits: str, scrolled_fruits: str, 
                             initial_count: int, scrolled_count: int):
        """Save targeted extraction results."""
        try:
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asda_targeted_extraction_{timestamp}.json"
            filepath = results_dir / filename
            
            data = {
                "test_info": {
                    "timestamp": timestamp,
                    "url": self.test_url,
                    "extraction_method": "targeted_fruit_extraction",
                    "initial_count": initial_count,
                    "scrolled_count": scrolled_count
                },
                "extractions": {
                    "initial_fruits": initial_fruits[:2000],  # Truncate for file size
                    "scrolled_fruits": scrolled_fruits[:2000]
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            console.print(f"📁 Targeted results saved to: {filepath}")
            
        except Exception as e:
            console.print(f"⚠️ Failed to save targeted results: {str(e)}")
    
    def _save_specific_search_results(self, found_fruits: dict):
        """Save specific fruit search results."""
        try:
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asda_specific_fruits_{timestamp}.json"
            filepath = results_dir / filename
            
            # Truncate results for file size
            truncated_fruits = {}
            for fruit_type, result in found_fruits.items():
                truncated_fruits[fruit_type] = result[:500] if result else ""
            
            data = {
                "test_info": {
                    "timestamp": timestamp,
                    "url": self.test_url,
                    "extraction_method": "specific_fruit_search"
                },
                "fruit_searches": truncated_fruits
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            console.print(f"📁 Specific search results saved to: {filepath}")
            
        except Exception as e:
            console.print(f"⚠️ Failed to save specific search results: {str(e)}")
    
    def run_targeted_test(self):
        """Run complete targeted extraction test."""
        console.print(Panel(
            "[bold green]🍎 ASDA Targeted Fruit Extraction Test[/bold green]\n\n"
            "Objectives:\n"
            "• Identify correct fruit product sections\n"
            "• Extract only fruit products (not promotional items)\n"
            "• Test scrolling for additional fruits\n"
            "• Validate with specific fruit type searches",
            title="Targeted Extraction Test"
        ))
        
        # Test 1: Page analysis
        page_analysis, fruit_analysis, grid_analysis = self.test_page_analysis()
        
        # Test 2: Targeted fruit extraction
        initial_count, scrolled_count, initial_fruits, scrolled_fruits = self.test_targeted_fruit_extraction()
        
        # Test 3: Specific fruit search
        found_fruits = self.test_specific_fruit_search()
        
        # Summary
        console.print("\n[bold green]📊 Targeted Test Summary[/bold green]")
        console.print(f"Initial fruit extraction: {initial_count} products")
        console.print(f"After scrolling: {scrolled_count} products")
        console.print(f"Specific fruit types found: {len([f for f in found_fruits.values() if f])}")
        
        # Determine if we found actual fruits
        if initial_count > 0 and any(keyword in initial_fruits.lower() for keyword in ["apple", "orange", "berry", "melon"]):
            console.print("\n✅ Successfully found actual fruit products!")
        else:
            console.print("\n⚠️ May still be extracting non-fruit items")
        
        return {
            "initial_count": initial_count,
            "scrolled_count": scrolled_count,
            "fruit_types_found": len([f for f in found_fruits.values() if f])
        }


def main():
    """Main test execution."""
    test = AsdaTargetedExtractionTest()
    results = test.run_targeted_test()
    
    # Exit with success if we found fruits
    success = results["initial_count"] > 0
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
