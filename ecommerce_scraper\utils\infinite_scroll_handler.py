"""
Infinite Scroll Handler for Dynamic Product Loading

Handles infinite scroll, lazy loading, and dynamic content loading patterns
commonly used by ecommerce sites.
"""

import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ScrollResult:
    """Result of a scroll operation."""
    success: bool
    products_before: int
    products_after: int
    new_products_loaded: int
    scroll_position: int
    has_more_content: bool
    error: Optional[str] = None


class InfiniteScrollHandler:
    """Handles infinite scroll and dynamic content loading for ecommerce sites."""
    
    def __init__(self, max_scrolls: int = 10, scroll_delay: float = 2.0):
        """
        Initialize the infinite scroll handler.
        
        Args:
            max_scrolls: Maximum number of scroll attempts
            scroll_delay: Delay between scroll operations (seconds)
        """
        self.max_scrolls = max_scrolls
        self.scroll_delay = scroll_delay
        
    def handle_infinite_scroll(self, tool, max_products: Optional[int] = None) -> List[ScrollResult]:
        """
        Handle infinite scroll to load all products.
        
        Args:
            tool: StagehandTool instance for browser interaction
            max_products: Maximum products to load (None for all)
            
        Returns:
            List of ScrollResult objects tracking the scroll progress
        """
        results = []
        scroll_count = 0
        
        try:
            # Get initial product count
            initial_count = self._count_products(tool)
            logger.info(f"Initial product count: {initial_count}")
            
            while scroll_count < self.max_scrolls:
                scroll_count += 1
                logger.info(f"Scroll attempt {scroll_count}/{self.max_scrolls}")
                
                # Count products before scroll
                products_before = self._count_products(tool)
                
                # Perform scroll
                scroll_success = self._scroll_page(tool)
                
                if not scroll_success:
                    results.append(ScrollResult(
                        success=False,
                        products_before=products_before,
                        products_after=products_before,
                        new_products_loaded=0,
                        scroll_position=scroll_count,
                        has_more_content=False,
                        error="Scroll operation failed"
                    ))
                    break
                
                # Wait for content to load
                time.sleep(self.scroll_delay)
                
                # Count products after scroll
                products_after = self._count_products(tool)
                new_products = products_after - products_before
                
                logger.info(f"Products before: {products_before}, after: {products_after}, new: {new_products}")
                
                # Check if we've reached the limit
                if max_products and products_after >= max_products:
                    logger.info(f"Reached product limit: {max_products}")
                    results.append(ScrollResult(
                        success=True,
                        products_before=products_before,
                        products_after=products_after,
                        new_products_loaded=new_products,
                        scroll_position=scroll_count,
                        has_more_content=True
                    ))
                    break
                
                # Check if no new products loaded
                if new_products == 0:
                    logger.info("No new products loaded - reached end")
                    results.append(ScrollResult(
                        success=True,
                        products_before=products_before,
                        products_after=products_after,
                        new_products_loaded=new_products,
                        scroll_position=scroll_count,
                        has_more_content=False
                    ))
                    break
                
                # Record successful scroll
                results.append(ScrollResult(
                    success=True,
                    products_before=products_before,
                    products_after=products_after,
                    new_products_loaded=new_products,
                    scroll_position=scroll_count,
                    has_more_content=True
                ))
                
        except Exception as e:
            logger.error(f"Infinite scroll handling failed: {e}")
            results.append(ScrollResult(
                success=False,
                products_before=0,
                products_after=0,
                new_products_loaded=0,
                scroll_position=scroll_count,
                has_more_content=False,
                error=str(e)
            ))
        
        return results
    
    def _count_products(self, tool) -> int:
        """Count the number of products currently visible on the page."""
        try:
            count_result = tool._run(
                instruction="Count the exact number of product items currently visible on this page. Look for product cards, tiles, or listings. Return just the number.",
                command_type="extract"
            )
            
            # Extract number from the result
            if isinstance(count_result, str):
                # Try to extract number from the response
                import re
                numbers = re.findall(r'\b\d+\b', count_result)
                if numbers:
                    return int(numbers[0])
            
            return 0
            
        except Exception as e:
            logger.error(f"Failed to count products: {e}")
            return 0
    
    def _scroll_page(self, tool) -> bool:
        """Scroll the page to load more content."""
        try:
            scroll_result = tool._run(
                instruction="Scroll down the page to load more products. Scroll slowly to allow content to load.",
                command_type="act"
            )
            
            # Check if scroll was successful
            return "scroll" in scroll_result.lower() or "down" in scroll_result.lower()
            
        except Exception as e:
            logger.error(f"Failed to scroll page: {e}")
            return False
    
    def extract_all_products_with_scroll(self, tool, max_products: Optional[int] = None) -> Tuple[str, List[ScrollResult]]:
        """
        Extract all products by handling infinite scroll.
        
        Args:
            tool: StagehandTool instance
            max_products: Maximum products to extract
            
        Returns:
            Tuple of (extracted_products_data, scroll_results)
        """
        # First, handle infinite scroll to load all products
        scroll_results = self.handle_infinite_scroll(tool, max_products)
        
        # Then extract all visible products
        try:
            all_products = tool._run(
                instruction=f"Extract ALL products currently visible on this page. Don't limit the extraction - get every single product with name, price, and details. There should be many products visible now.",
                command_type="extract"
            )
            
            return all_products, scroll_results
            
        except Exception as e:
            logger.error(f"Failed to extract all products: {e}")
            return "", scroll_results
    
    @staticmethod
    def detect_pagination_type(tool) -> str:
        """Detect the type of pagination used on the page."""
        try:
            pagination_analysis = tool._run(
                instruction="Analyze this page for pagination mechanisms. Look for: numbered pagination (1,2,3...), 'Load More' buttons, 'Show More' buttons, infinite scroll indicators, or text like 'Showing X of Y items'. Describe what you find.",
                command_type="extract"
            )
            
            pagination_lower = pagination_analysis.lower()
            
            if "infinite scroll" in pagination_lower or "scroll" in pagination_lower:
                return "infinite_scroll"
            elif "load more" in pagination_lower or "show more" in pagination_lower:
                return "load_more"
            elif any(num in pagination_lower for num in ["1", "2", "3", "next", "previous"]):
                return "numbered"
            elif "showing" in pagination_lower and "of" in pagination_lower:
                return "infinite_scroll"  # Often indicates lazy loading
            else:
                return "unknown"
                
        except Exception as e:
            logger.error(f"Failed to detect pagination type: {e}")
            return "unknown"


# Vendor-specific scroll configurations
VENDOR_SCROLL_CONFIGS = {
    "asda": {
        "max_scrolls": 15,
        "scroll_delay": 3.0,
        "pagination_type": "infinite_scroll"
    },
    "tesco": {
        "max_scrolls": 10,
        "scroll_delay": 2.5,
        "pagination_type": "load_more"
    },
    "waitrose": {
        "max_scrolls": 8,
        "scroll_delay": 2.0,
        "pagination_type": "numbered"
    },
    "default": {
        "max_scrolls": 10,
        "scroll_delay": 2.0,
        "pagination_type": "unknown"
    }
}


def get_scroll_handler_for_vendor(vendor: str) -> InfiniteScrollHandler:
    """Get a configured scroll handler for a specific vendor."""
    config = VENDOR_SCROLL_CONFIGS.get(vendor, VENDOR_SCROLL_CONFIGS["default"])
    
    return InfiniteScrollHandler(
        max_scrolls=config["max_scrolls"],
        scroll_delay=config["scroll_delay"]
    )
