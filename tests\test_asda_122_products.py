#!/usr/bin/env python3
"""
ASDA 122 Products Validation Test

Confirms that we can actually scrape all 122 fruit products using the infinite scroll handler.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool
from ecommerce_scraper.utils.infinite_scroll_handler import InfiniteScrollHandler

console = Console()

class Asda122ProductsTest:
    """Test to confirm we can extract all 122 ASDA fruit products."""
    
    def __init__(self):
        self.test_url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
        self.target_count = 122
        
    def test_step_by_step_scroll(self):
        """Test step-by-step scrolling with detailed tracking."""
        console.print("🔄 Testing step-by-step infinite scroll...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                console.print(f"📍 Navigating to ASDA fruit page...")
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(4)
                
                # Check initial state
                console.print("📊 Checking initial product count...")
                initial_check = tool._run(
                    instruction="Look at the page and tell me: 1) How many products are currently visible? 2) Is there any text showing 'Showing X of Y items'? 3) Are there any 'Load More' or pagination controls?",
                    command_type="extract"
                )
                console.print(f"Initial state: {initial_check}")
                
                # Count initial products
                initial_count = self._get_product_count(tool)
                console.print(f"📦 Initial products visible: {initial_count}")
                
                # Progressive scrolling with tracking
                scroll_results = []
                max_scrolls = 10
                
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    scroll_task = progress.add_task("Scrolling to load products...", total=max_scrolls)
                    
                    for scroll_num in range(1, max_scrolls + 1):
                        progress.update(scroll_task, description=f"Scroll {scroll_num}/{max_scrolls}")
                        
                        # Count before scroll
                        before_count = self._get_product_count(tool)
                        
                        # Perform scroll
                        console.print(f"\n🔄 Scroll {scroll_num}: {before_count} products visible")
                        scroll_result = tool._run(
                            instruction="Scroll down the page slowly to load more fruit products. Scroll about 3-4 screen heights down.",
                            command_type="act"
                        )
                        
                        # Wait for content to load
                        time.sleep(5)
                        
                        # Count after scroll
                        after_count = self._get_product_count(tool)
                        new_products = after_count - before_count
                        
                        scroll_info = {
                            "scroll_number": scroll_num,
                            "before_count": before_count,
                            "after_count": after_count,
                            "new_products": new_products,
                            "scroll_result": scroll_result[:100]
                        }
                        scroll_results.append(scroll_info)
                        
                        console.print(f"📊 After scroll {scroll_num}: {after_count} products (+{new_products} new)")
                        
                        # Check if we've reached the target
                        if after_count >= self.target_count:
                            console.print(f"🎯 Target reached! Found {after_count} products")
                            break
                        
                        # Check if no new products loaded
                        if new_products == 0:
                            console.print("⏹️ No new products loaded - reached end")
                            break
                        
                        progress.advance(scroll_task)
                
                # Final product extraction
                console.print("\n📋 Extracting all visible products...")
                final_extraction = tool._run(
                    instruction="Extract ALL fruit products currently visible on this page. For each product, get: name, price, and any special offers. Number each product (1., 2., 3., etc.). Focus only on actual fruit products, ignore promotional banners.",
                    command_type="extract"
                )
                
                # Count final products
                final_count = self._count_products_in_extraction(final_extraction)
                console.print(f"📊 Final extraction count: {final_count} products")
                
                # Save detailed results
                self._save_detailed_results(scroll_results, final_extraction, final_count)
                
                return scroll_results, final_extraction, final_count
                
        except Exception as e:
            console.print(f"❌ Step-by-step scroll test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return [], "", 0
    
    def test_automated_scroll_handler(self):
        """Test the automated infinite scroll handler."""
        console.print("\n🤖 Testing automated infinite scroll handler...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(4)
                
                # Use the infinite scroll handler
                scroll_handler = InfiniteScrollHandler(max_scrolls=15, scroll_delay=4.0)
                
                console.print("🔄 Running automated scroll handler...")
                all_products, scroll_results = scroll_handler.extract_all_products_with_scroll(
                    tool, max_products=self.target_count
                )
                
                # Count products
                automated_count = self._count_products_in_extraction(all_products)
                console.print(f"📊 Automated handler found: {automated_count} products")
                
                # Display scroll results
                self._display_scroll_table(scroll_results)
                
                return all_products, scroll_results, automated_count
                
        except Exception as e:
            console.print(f"❌ Automated scroll handler test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return "", [], 0
    
    def _get_product_count(self, tool) -> int:
        """Get current product count on page."""
        try:
            count_result = tool._run(
                instruction="Count exactly how many fruit products are currently visible on this page. Look for product cards/tiles with fruit items. Give me just the number.",
                command_type="extract"
            )
            
            # Extract number from result
            import re
            numbers = re.findall(r'\b\d+\b', count_result)
            if numbers:
                # Take the largest number found (likely the product count)
                return max(int(num) for num in numbers)
            return 0
            
        except Exception as e:
            console.print(f"⚠️ Failed to get product count: {e}")
            return 0
    
    def _count_products_in_extraction(self, extraction: str) -> int:
        """Count products in extraction result."""
        if not extraction:
            return 0
        
        # Count numbered items (1., 2., 3., etc.)
        import re
        numbered_items = re.findall(r'^\s*\d+\.', extraction, re.MULTILINE)
        if numbered_items:
            return len(numbered_items)
        
        # Count lines with product indicators
        lines = extraction.split('\n')
        product_lines = [line for line in lines if any(indicator in line.lower() 
                        for indicator in ['£', 'price:', 'asda', 'fruit', 'berry'])]
        
        return len(product_lines)
    
    def _display_scroll_table(self, scroll_results):
        """Display scroll results in a formatted table."""
        if not scroll_results:
            return
        
        table = Table(title="Scroll Progress")
        table.add_column("Scroll #", style="cyan", width=8)
        table.add_column("Before", style="magenta", width=8)
        table.add_column("After", style="green", width=8)
        table.add_column("New", style="yellow", width=6)
        table.add_column("Success", style="blue", width=8)
        table.add_column("More Content", style="white", width=12)
        
        for i, result in enumerate(scroll_results, 1):
            table.add_row(
                str(i),
                str(result.products_before),
                str(result.products_after),
                str(result.new_products_loaded),
                "✅" if result.success else "❌",
                "Yes" if result.has_more_content else "No"
            )
        
        console.print(table)
    
    def _save_detailed_results(self, scroll_results, final_extraction, final_count):
        """Save detailed test results."""
        try:
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asda_122_products_test_{timestamp}.json"
            filepath = results_dir / filename
            
            data = {
                "test_info": {
                    "timestamp": timestamp,
                    "url": self.test_url,
                    "target_count": self.target_count,
                    "final_count": final_count,
                    "success": final_count >= self.target_count * 0.9  # 90% success threshold
                },
                "scroll_progress": scroll_results,
                "final_extraction": final_extraction[:10000],  # Truncate for file size
                "summary": {
                    "total_scrolls": len(scroll_results),
                    "max_products_found": max([r.get("after_count", 0) for r in scroll_results] + [0]),
                    "extraction_method": "step_by_step_scroll"
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            console.print(f"📁 Detailed results saved to: {filepath}")
            
        except Exception as e:
            console.print(f"⚠️ Failed to save detailed results: {str(e)}")
    
    def run_validation_test(self):
        """Run complete validation to confirm 122 products can be scraped."""
        console.print(Panel(
            "[bold blue]🎯 ASDA 122 Products Validation Test[/bold blue]\n\n"
            f"Target: Extract all {self.target_count} fruit products\n"
            "Methods:\n"
            "• Step-by-step scrolling with detailed tracking\n"
            "• Automated infinite scroll handler\n"
            "• Product count validation\n"
            "• Success criteria: ≥90% of target products",
            title="122 Products Validation"
        ))
        
        # Test 1: Step-by-step scrolling
        scroll_results, final_extraction, step_count = self.test_step_by_step_scroll()
        
        # Test 2: Automated scroll handler
        auto_extraction, auto_scroll_results, auto_count = self.test_automated_scroll_handler()
        
        # Results summary
        console.print("\n[bold green]📊 Validation Results[/bold green]")
        
        results_table = Table(title="Test Results Summary")
        results_table.add_column("Method", style="cyan")
        results_table.add_column("Products Found", style="green")
        results_table.add_column("Target", style="yellow")
        results_table.add_column("Success Rate", style="magenta")
        results_table.add_column("Status", style="blue")
        
        step_success_rate = (step_count / self.target_count) * 100 if step_count > 0 else 0
        auto_success_rate = (auto_count / self.target_count) * 100 if auto_count > 0 else 0
        
        results_table.add_row(
            "Step-by-step",
            str(step_count),
            str(self.target_count),
            f"{step_success_rate:.1f}%",
            "✅ PASS" if step_success_rate >= 90 else "❌ FAIL"
        )
        
        results_table.add_row(
            "Automated",
            str(auto_count),
            str(self.target_count),
            f"{auto_success_rate:.1f}%",
            "✅ PASS" if auto_success_rate >= 90 else "❌ FAIL"
        )
        
        console.print(results_table)
        
        # Overall assessment
        best_count = max(step_count, auto_count)
        best_rate = (best_count / self.target_count) * 100
        
        if best_rate >= 90:
            console.print(f"\n[bold green]🎉 SUCCESS![/bold green] Found {best_count}/{self.target_count} products ({best_rate:.1f}%)")
            console.print("✅ Infinite scroll handler is working correctly!")
            console.print("✅ Ready for integration into main scraper!")
        elif best_rate >= 70:
            console.print(f"\n[bold yellow]⚠️ PARTIAL SUCCESS[/bold yellow] Found {best_count}/{self.target_count} products ({best_rate:.1f}%)")
            console.print("🔧 Needs refinement before integration")
        else:
            console.print(f"\n[bold red]❌ NEEDS WORK[/bold red] Found {best_count}/{self.target_count} products ({best_rate:.1f}%)")
            console.print("🔧 Requires significant improvements")
        
        return {
            "step_count": step_count,
            "auto_count": auto_count,
            "target": self.target_count,
            "best_rate": best_rate,
            "ready_for_integration": best_rate >= 90
        }


def main():
    """Main validation execution."""
    test = Asda122ProductsTest()
    results = test.run_validation_test()
    
    # Exit with success if we achieved 90% or better
    success = results["ready_for_integration"]
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
