#!/usr/bin/env python3
"""
ASDA Full Product Extraction Test

Tests the enhanced extraction with infinite scroll handling to get all 122 products.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool
from ecommerce_scraper.utils.infinite_scroll_handler import InfiniteScrollHandler, get_scroll_handler_for_vendor

console = Console()

class AsdaFullExtractionTest:
    """Test full product extraction with infinite scroll handling."""
    
    def __init__(self):
        self.test_url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
        self.expected_total = 122  # Based on debug findings
        
    def test_basic_extraction(self):
        """Test basic extraction without scroll handling."""
        console.print("📋 Testing basic extraction (current method)...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                # Extract products without scrolling
                basic_result = tool._run(
                    instruction="Extract all products currently visible on this page. Get name and price for each product.",
                    command_type="extract"
                )
                
                # Count products in result
                product_count = self._count_products_in_result(basic_result)
                console.print(f"📊 Basic extraction found: {product_count} products")
                console.print(f"📄 Sample result: {basic_result[:300]}...")
                
                return product_count, basic_result
                
        except Exception as e:
            console.print(f"❌ Basic extraction failed: {str(e)}")
            return 0, ""
    
    def test_scroll_enhanced_extraction(self):
        """Test extraction with infinite scroll handling."""
        console.print("\n🔄 Testing scroll-enhanced extraction...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                # Use ASDA-specific scroll handler
                scroll_handler = get_scroll_handler_for_vendor("asda")
                
                # Extract all products with scroll handling
                all_products, scroll_results = scroll_handler.extract_all_products_with_scroll(
                    tool, max_products=self.expected_total
                )
                
                # Count products in result
                product_count = self._count_products_in_result(all_products)
                console.print(f"📊 Scroll-enhanced extraction found: {product_count} products")
                
                # Display scroll results
                self._display_scroll_results(scroll_results)
                
                # Save detailed results
                self._save_extraction_results(all_products, scroll_results, product_count)
                
                return product_count, all_products, scroll_results
                
        except Exception as e:
            console.print(f"❌ Scroll-enhanced extraction failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0, "", []
    
    def test_manual_scroll_approach(self):
        """Test manual scroll approach with multiple extraction passes."""
        console.print("\n🖱️ Testing manual scroll approach...")
        
        try:
            with EcommerceStagehandTool.create_with_context() as tool:
                # Navigate to page
                tool._run(
                    instruction="Navigate to the ASDA fruit page",
                    url=self.test_url,
                    command_type="act"
                )
                time.sleep(3)
                
                all_products = []
                
                # Extract initial products
                initial_products = tool._run(
                    instruction="Extract the first batch of products visible on this page. Get name and price for each.",
                    command_type="extract"
                )
                all_products.append(("Initial", initial_products))
                console.print(f"📦 Initial batch extracted")
                
                # Scroll and extract multiple times
                for i in range(3):
                    console.print(f"🔄 Scroll {i+1}...")
                    
                    # Scroll down
                    tool._run(
                        instruction="Scroll down the page slowly to load more products",
                        command_type="act"
                    )
                    time.sleep(4)  # Wait for content to load
                    
                    # Extract new products
                    new_products = tool._run(
                        instruction=f"Extract all products now visible on this page after scrolling. This is scroll {i+1}.",
                        command_type="extract"
                    )
                    all_products.append((f"Scroll {i+1}", new_products))
                    console.print(f"📦 Scroll {i+1} batch extracted")
                
                # Combine and analyze results
                total_extractions = len(all_products)
                console.print(f"📊 Total extraction passes: {total_extractions}")
                
                # Save manual scroll results
                self._save_manual_scroll_results(all_products)
                
                return all_products
                
        except Exception as e:
            console.print(f"❌ Manual scroll approach failed: {str(e)}")
            return []
    
    def _count_products_in_result(self, result: str) -> int:
        """Count products in extraction result."""
        if not result:
            return 0
        
        # Count numbered items (1., 2., 3., etc.)
        import re
        numbered_items = re.findall(r'^\d+\.', result, re.MULTILINE)
        if numbered_items:
            return len(numbered_items)
        
        # Count bullet points
        bullet_items = re.findall(r'^[-*•]', result, re.MULTILINE)
        if bullet_items:
            return len(bullet_items)
        
        # Count product names (rough estimate)
        product_indicators = re.findall(r'(ASDA|Scotty|Brand)', result, re.IGNORECASE)
        return len(product_indicators)
    
    def _display_scroll_results(self, scroll_results):
        """Display scroll results in a table."""
        if not scroll_results:
            return
        
        table = Table(title="Scroll Results")
        table.add_column("Scroll #", style="cyan")
        table.add_column("Before", style="magenta")
        table.add_column("After", style="green")
        table.add_column("New", style="yellow")
        table.add_column("Success", style="blue")
        
        for i, result in enumerate(scroll_results, 1):
            table.add_row(
                str(i),
                str(result.products_before),
                str(result.products_after),
                str(result.new_products_loaded),
                "✅" if result.success else "❌"
            )
        
        console.print(table)
    
    def _save_extraction_results(self, products_data: str, scroll_results, product_count: int):
        """Save extraction results to file."""
        try:
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asda_full_extraction_{timestamp}.json"
            filepath = results_dir / filename
            
            data = {
                "test_info": {
                    "timestamp": timestamp,
                    "url": self.test_url,
                    "expected_total": self.expected_total,
                    "extracted_count": product_count,
                    "extraction_method": "scroll_enhanced"
                },
                "scroll_results": [
                    {
                        "scroll_position": r.scroll_position,
                        "products_before": r.products_before,
                        "products_after": r.products_after,
                        "new_products_loaded": r.new_products_loaded,
                        "success": r.success,
                        "has_more_content": r.has_more_content,
                        "error": r.error
                    } for r in scroll_results
                ],
                "products_data": products_data[:5000]  # Truncate for file size
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            console.print(f"📁 Results saved to: {filepath}")
            
        except Exception as e:
            console.print(f"⚠️ Failed to save results: {str(e)}")
    
    def _save_manual_scroll_results(self, all_products):
        """Save manual scroll results."""
        try:
            results_dir = Path("test_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asda_manual_scroll_{timestamp}.json"
            filepath = results_dir / filename
            
            data = {
                "test_info": {
                    "timestamp": timestamp,
                    "url": self.test_url,
                    "extraction_method": "manual_scroll",
                    "total_passes": len(all_products)
                },
                "extraction_passes": [
                    {
                        "pass_name": pass_name,
                        "data": data[:1000]  # Truncate for file size
                    } for pass_name, data in all_products
                ]
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            console.print(f"📁 Manual scroll results saved to: {filepath}")
            
        except Exception as e:
            console.print(f"⚠️ Failed to save manual scroll results: {str(e)}")
    
    def run_full_test(self):
        """Run complete extraction test suite."""
        console.print(Panel(
            "[bold blue]🧪 ASDA Full Product Extraction Test[/bold blue]\n\n"
            f"Target: Extract all {self.expected_total} products from ASDA Fruit category\n"
            "Methods:\n"
            "• Basic extraction (current method)\n"
            "• Scroll-enhanced extraction (new method)\n"
            "• Manual scroll approach (fallback)",
            title="Full Extraction Test"
        ))
        
        # Test 1: Basic extraction
        basic_count, basic_result = self.test_basic_extraction()
        
        # Test 2: Scroll-enhanced extraction
        scroll_count, scroll_result, scroll_results = self.test_scroll_enhanced_extraction()
        
        # Test 3: Manual scroll approach
        manual_results = self.test_manual_scroll_approach()
        
        # Summary
        console.print("\n[bold green]📊 Test Summary[/bold green]")
        console.print(f"Expected products: {self.expected_total}")
        console.print(f"Basic extraction: {basic_count} products")
        console.print(f"Scroll-enhanced: {scroll_count} products")
        console.print(f"Manual scroll passes: {len(manual_results)}")
        
        # Determine best method
        if scroll_count >= basic_count * 2:
            console.print("\n✅ Scroll-enhanced extraction is significantly better!")
        elif scroll_count > basic_count:
            console.print("\n✅ Scroll-enhanced extraction shows improvement!")
        else:
            console.print("\n⚠️ Scroll-enhanced extraction needs refinement")
        
        return {
            "basic_count": basic_count,
            "scroll_count": scroll_count,
            "manual_passes": len(manual_results),
            "expected": self.expected_total
        }


def main():
    """Main test execution."""
    test = AsdaFullExtractionTest()
    results = test.run_full_test()
    
    # Exit with success if we got more products than basic method
    improvement = results["scroll_count"] > results["basic_count"]
    sys.exit(0 if improvement else 1)


if __name__ == "__main__":
    main()
